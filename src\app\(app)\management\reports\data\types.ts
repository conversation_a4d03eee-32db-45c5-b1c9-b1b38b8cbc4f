export interface Cargo {
  secureId: string;
  cargo_nome: string;
  createdAt: string;
  updatedAt: string;
}

export interface Gerencia {
  secureId: string;
  gerencia_nome: string;
  cargos: Cargo[];
}

export interface Superintendencia {
  secureId: string;
  superintendencia_nome: string;
  gerencias: Gerencia[];
}

export interface Segment {
  secureId: string;
  segmento_nome: string;
  superintendencias: Superintendencia[];
}

export interface ReportsData {
  data: Segment[];
}

// Hook return types
export interface UseHierarchicalSelectionReturn {
  selectedItems: Set<string>;
  allSelected: boolean;
  handleItemChange: (id: string, checked: boolean) => void;
  handleAllChange: (checked: boolean) => void;
}

export interface UseRoundSelectionReturn {
  selectedRounds: Set<string>;
  handleRoundChange: (round: string, checked: boolean) => void;
}

export interface UseSearchFilterReturn {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
}
