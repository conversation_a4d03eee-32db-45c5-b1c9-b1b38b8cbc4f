"use client";
import {
  Box,
  Flex,
  HStack,
  Image,
  Input,
  InputGroup,
  Spinner,
  Text,
  VStack,
} from "@chakra-ui/react";
import { useHierarchicalSelection } from "./hooks/useHierarchicalSelection";
import { useRoundSelection } from "./hooks/useRoundSelection";
import { ReportsHeader } from "./components/ReportsHeader";
import { ReportsFilters } from "./components/ReportsFilters";
import { SegmentDisplay } from "./components/SegmentDisplay";
import { ReportResults } from "./components/ReportResults";
import { Chart, useChart } from "@chakra-ui/charts";
import {
  Bar,
  Bar<PERSON>hart,
  CartesianGrid,
  LabelList,
  Legend,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";
import { LuSearch } from "react-icons/lu";
import { useState } from "react";
import { useGetHierarchyReports } from "@/hook/reports/useGetHierarchyReports";
import { useGenerateReport } from "@/hook/reports/useGenerateReport";
import {
  validateReportSelections,
  createReportRequest,
} from "./utils/reportDataCollector";
import { ReportGenerationResponse } from "./data/types";
import { toaster } from "@/components/ui/toaster";

// 1. Dados para o gráfico de NÚMEROS BRUTOS
const rawChartData = [
  { name: "0", checkin: 2, checkout: 1 },
  { name: "1", checkin: 5, checkout: 3 },
  { name: "2", checkin: 8, checkout: 6 },
  { name: "3", checkin: 3, checkout: 5 },
  { name: "4", checkin: 12, checkout: 15 },
  { name: "5", checkin: 10, checkout: 11 },
];

// 2. Dados para o gráfico de PORCENTAGEM (já calculados)
const percentageChartData = [
  { name: "0", checkin: 5.0, checkout: 2.4 },
  { name: "1", checkin: 12.5, checkout: 7.3 },
  { name: "2", checkin: 20.0, checkout: 14.6 },
  { name: "3", checkin: 7.5, checkout: 12.2 },
  { name: "4", checkin: 30.0, checkout: 36.6 },
  { name: "5", checkin: 25.0, checkout: 26.8 },
];

export default function Reports() {
  const [searchTerm, setSearchTerm] = useState("");
  const [reportData, setReportData] = useState<ReportGenerationResponse | null>(
    null
  );
  const [showResults, setShowResults] = useState(false);
  const [isSingleResult, setIsSingleResult] = useState(false);

  const { data: hierarchyReportsData, isLoading } = useGetHierarchyReports();
  console.log("data: ", hierarchyReportsData);

  const { selectedItems, allSelected, handleItemChange, handleAllChange } =
    useHierarchicalSelection(hierarchyReportsData?.data || []);
  const { selectedRounds, handleRoundChange } = useRoundSelection();

  const generateReportMutation = useGenerateReport();

  // Validation for Generate Report button
  const isGenerateDisabled =
    !hierarchyReportsData?.data ||
    selectedRounds.size === 0 ||
    selectedItems.size === 0;

  // Handle report generation
  const handleGenerateReport = async () => {
    if (!hierarchyReportsData?.data) {
      toaster.error({
        title: "Erro",
        description: "Dados da hierarquia não carregados",
      });
      return;
    }

    // Validate selections
    const validation = validateReportSelections(
      selectedRounds,
      selectedItems,
      hierarchyReportsData.data
    );

    if (!validation.isValid) {
      toaster.error({
        title: "Seleção inválida",
        description: validation.message,
      });
      return;
    }

    try {
      // Create request payload
      const requestData = createReportRequest(
        selectedRounds,
        selectedItems,
        hierarchyReportsData.data
      );

      // Generate report
      const response = await generateReportMutation.mutateAsync(requestData);
      setReportData(response);
      setIsSingleResult(requestData.filters.length === 1);
      setShowResults(true);
    } catch (error) {
      console.error("Error generating report:", error);
    }
  };

  if (isLoading || !hierarchyReportsData) {
    return (
      <Flex flex={1} justify="center" align="center">
        <Spinner size="xl" />
      </Flex>
    );
  }

  return (
    <Flex flex={1} position="relative" overflowX="auto">
      <VStack w="100%" gap={6} align="stretch">
        <VStack p={6} align="stretch">
          {/* Header */}
          <ReportsHeader
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
            onGenerateReport={handleGenerateReport}
            isGenerateDisabled={isGenerateDisabled}
            isGenerating={generateReportMutation.isPending}
          />

          {/* Main Content */}
          <HStack align="flex-start" gap={8}>
            {/* Controls */}
            <ReportsFilters
              allSelected={allSelected}
              selectedRounds={selectedRounds}
              onAllChange={handleAllChange}
              onRoundChange={handleRoundChange}
            />

            {/* Segments */}
            <SegmentDisplay
              segments={hierarchyReportsData?.data}
              selectedItems={selectedItems}
              onItemChange={handleItemChange}
            />
          </HStack>
        </VStack>

        {/* Report Results */}
        {showResults && reportData && (
          <ReportResults
            reportData={reportData}
            isSingleResult={isSingleResult}
          />
        )}

        {/* Single Result - Static (shown when no report generated) */}
        {!showResults && (
          <Box w="100%" position="relative" overflow="hidden" flexShrink={0}>
            <Box
              position="absolute"
              top={0}
              right={0}
              transform={"scale(2.5)"}
              w={"10%"}
              h={"100%"}
              bgImg="url(/images/padraoBG-01.svg)"
              bgRepeat="no-repeat"
              bgPos="center left"
              bgSize="cover"
              clipPath="inset(0 0 0 0)"
            />
            <VStack w="100%" gap={6} align="stretch" p={6}>
              <HStack gap={8}>
                <Image
                  src="/images/logoBancoABC.svg"
                  alt="Banco ABC"
                  w="100px"
                  h="auto"
                />
                <HStack gap={4} w={"50%"}>
                  <Box
                    bgColor={"gray.300"}
                    color={"black"}
                    borderRadius={"lg"}
                    p={3}
                  >
                    <Text color={"black"} fontSize={"md"}>
                      Nome:
                    </Text>
                  </Box>

                  <Box
                    bgColor={"gray.300"}
                    color={"black"}
                    borderRadius={"lg"}
                    p={3}
                  >
                    <Text color={"black"} fontSize={"md"}>
                      Check-in:
                    </Text>
                  </Box>
                  <Box
                    bgColor={"gray.300"}
                    color={"black"}
                    borderRadius={"lg"}
                    p={3}
                  >
                    <Text color={"black"} fontSize={"md"}>
                      Check-out:
                    </Text>
                  </Box>
                </HStack>
              </HStack>
              <HStack gap={20} px={20} align="stretch">
                <VStack gap={8} py={8} justifyContent="space-around">
                  <Box bg={"#a6864a"} px={10} py={6} borderRadius={"4xl"}>
                    <Text color={"white"} fontSize={"lg"} textAlign={"center"}>
                      Lorem ipsum dolor sit amet, consectetur adipiscing
                      elit.Lorem ipsum dolor sit amet, consectetur adipiscing
                      elit.Lorem ipsum dolor sit amet, consectetur adipiscing
                      elit.Lorem ipsum dolor sit amet, consectetur adipiscing
                      elit.Lorem ipsum dolor sit amet, consectetur adipiscing
                      elit.Lorem ipsum dolor sit amet, consectetur adipiscing
                      elit.Lorem ipsum dolor sit amet, consectetur adipiscing
                      elit.
                    </Text>
                  </Box>
                  <VStack gap={4} w={"50%"}>
                    <Box
                      bgColor={"gray.300"}
                      color={"black"}
                      borderRadius={"lg"}
                      p={3}
                    >
                      <Text color={"black"} fontSize={"md"}>
                        Check-in- Proficiência Geral:
                      </Text>
                    </Box>
                    <Box
                      bgColor={"gray.300"}
                      color={"black"}
                      borderRadius={"lg"}
                      p={3}
                    >
                      <Text color={"black"} fontSize={"md"}>
                        Check-out- Proficiência Geral:
                      </Text>
                    </Box>
                  </VStack>
                  <Text color={"white"} fontSize={"lg"} textAlign={"center"}>
                    Lorem ipsum dolor sit amet, consectetur adipiscing
                    elit.Lorem ipsum dolor sit amet, consectetur adipiscing
                    elit.Lorem ipsum dolor sit amet, consectetur adipiscing
                    elit.Lorem ipsum dolor sit amet, consectetur adipiscing
                    elit.Lorem ipsum dolor sit amet, consectetur adipiscing
                    elit.Lorem ipsum dolor sit amet, consectetur adipiscing
                    elit.Lorem ipsum dolor sit amet, consectetur adipiscing
                    elit.
                  </Text>
                </VStack>
                <Image
                  src="/images/mandala.png"
                  alt="Banco ABC"
                  w="800px"
                  zIndex={1}
                />
              </HStack>
            </VStack>
          </Box>
        )}

        {/* Multiple Results */}
        <Box w="100%" position="relative" overflow="hidden" flexShrink={0}>
          <VStack w="100%" gap={8} align="stretch" p={6}>
            <HStack gap={8}>
              <Image
                src="/images/logoBancoABC.svg"
                alt="Banco ABC"
                w="100px"
                h="auto"
              />
              <HStack gap={4} w={"50%"}>
                <Box
                  bgColor={"gray.300"}
                  color={"black"}
                  borderRadius={"lg"}
                  py={3}
                  px={6}
                >
                  <Text color={"black"} fontSize={"md"}>
                    Check-in
                  </Text>
                  <Text color={"black"} fontSize={"md"}>
                    Quantidade de elegíveis à responder:
                  </Text>
                  <Text color={"black"} fontSize={"md"}>
                    Quantidade de respondentes:
                  </Text>
                </Box>
                <Box
                  bgColor={"gray.300"}
                  color={"black"}
                  borderRadius={"lg"}
                  py={3}
                  px={6}
                >
                  <Text color={"black"} fontSize={"md"}>
                    Check-out
                  </Text>
                  <Text color={"black"} fontSize={"md"}>
                    Quantidade de elegíveis à responder:
                  </Text>
                  <Text color={"black"} fontSize={"md"}>
                    Quantidade de respondentes:
                  </Text>
                </Box>
              </HStack>
            </HStack>
            <HStack gap={20} w={"80%"} alignSelf={"center"}>
              <Image src="/images/mandala.png" alt="Banco ABC" w="500px" />
              <Text fontSize={"3xl"}>
                "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam
                ullamcorper, felis sed ultricies faucibus, justo libero feugiat
                turpis, in tincidunt nisi nulla sed purus. Vestibulum auctor,
                nunc nec varius vehicula, sapien nunc hendrerit neque, at
                ultricies dolor nisi nec lorem. Cras euismod arcu sit amet diam
                libero."
              </Text>
            </HStack>

            <Box px={6}>
              <HStack
                w="100%"
                bg="#393B3D"
                borderRadius="4xl"
                gap={0}
                align="stretch"
              >
                {/* PAINEL 1: PROFICIÊNCIA GERAL */}
                <Flex
                  flex={1.5}
                  color="white"
                  justify="center"
                  align="center"
                  direction="column"
                  gap={4}
                >
                  <HStack>
                    <Text fontSize="lg">Proficiência Geral:</Text>
                    <Box
                      border="1px solid white"
                      borderRadius="md"
                      px={1}
                      py={1}
                    >
                      <Text fontSize="lg">4.0</Text>
                    </Box>
                    <Box
                      border="1px solid white"
                      borderRadius="md"
                      px={1}
                      py={1}
                    >
                      <Text fontSize="lg">4.0</Text>
                    </Box>
                  </HStack>
                </Flex>

                {/* PAINEL 2: GRÁFICO DE PORCENTAGEM */}
                <VStack
                  flex={3}
                  bg="#B2B2B2"
                  borderLeftRadius="4xl"
                  p={4}
                  gap={4}
                  h="200px"
                  position={"relative"}
                >
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={percentageChartData}
                      margin={{ top: 25, right: 60 }}
                    >
                      <XAxis dataKey="name" stroke="black" tickLine={false} />
                      <Tooltip cursor={{ fill: "rgba(255, 255, 255, 0.1)" }} />
                      <Bar dataKey="checkin" fill="#104862" barSize={24}>
                        <LabelList
                          dataKey="checkin"
                          position="top"
                          fill="black"
                          formatter={(label: React.ReactNode) => {
                            if (typeof label === "number")
                              return `%\n${label.toFixed(1)}`;
                            return label;
                          }}
                          style={{ fontSize: "12px" }}
                        />
                      </Bar>
                      <Bar dataKey="checkout" fill="#00B0F0" barSize={24}>
                        <LabelList
                          dataKey="checkout"
                          position="top"
                          fill="black"
                          formatter={(label: React.ReactNode) => {
                            if (typeof label === "number")
                              return `%\n${label.toFixed(1)}`;
                            return label;
                          }}
                          style={{ fontSize: "12px" }}
                        />
                      </Bar>
                    </BarChart>
                  </ResponsiveContainer>
                </VStack>

                {/* PAINEL 3: GRÁFICO DE NÚMEROS BRUTOS */}
                <VStack
                  flex={3}
                  bg="#DADADA"
                  borderLeftRadius="4xl"
                  p={4}
                  gap={4}
                  h="200px"
                  position={"relative"}
                  ml={-16}
                >
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={rawChartData} margin={{ top: 25 }}>
                      <XAxis dataKey="name" stroke="black" tickLine={false} />
                      <Tooltip cursor={{ fill: "rgba(0, 0, 0, 0.1)" }} />
                      <Bar dataKey="checkin" fill="#104862" barSize={24}>
                        <LabelList
                          dataKey="checkin"
                          position="top"
                          fill="black"
                          style={{ fontSize: "12px" }}
                        />
                      </Bar>
                      <Bar dataKey="checkout" fill="#00B0F0" barSize={24}>
                        <LabelList
                          dataKey="checkout"
                          position="top"
                          fill="black"
                          style={{ fontSize: "12px" }}
                        />
                      </Bar>
                    </BarChart>
                  </ResponsiveContainer>
                </VStack>
              </HStack>
            </Box>
          </VStack>
        </Box>
        {/* )} */}
      </VStack>
    </Flex>
  );
}
