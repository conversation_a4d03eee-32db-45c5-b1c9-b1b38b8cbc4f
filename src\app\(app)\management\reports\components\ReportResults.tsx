import {
  <PERSON>,
  VStack,
  HStack,
  Text,
  Image,
  Flex,
} from "@chakra-ui/react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  CartesianGrid,
  LabelList,
  ResponsiveContainer,
  Tooltip,
  XAxis,
} from "recharts";
import { ReportGenerationResponse } from "../data/types";

interface ReportResultsProps {
  reportData: ReportGenerationResponse;
  isSingleResult: boolean;
}

export function ReportResults({ reportData, isSingleResult }: ReportResultsProps) {
  // Transform API data to chart format
  const transformToChartData = (dimension: any) => {
    return [
      { name: "1", checkin: dimension.checkIn["proficiency-1"], checkout: dimension.checkOut["proficiency-1"] },
      { name: "2", checkin: dimension.checkIn["proficiency-2"], checkout: dimension.checkOut["proficiency-2"] },
      { name: "3", checkin: dimension.checkIn["proficiency-3"], checkout: dimension.checkOut["proficiency-3"] },
      { name: "4", checkin: dimension.checkIn["proficiency-4"], checkout: dimension.checkOut["proficiency-4"] },
      { name: "5", checkin: dimension.checkIn["proficiency-5"], checkout: dimension.checkOut["proficiency-5"] },
    ];
  };

  if (isSingleResult) {
    return (
      <Box w="100%" position="relative" overflow="hidden" flexShrink={0}>
        <Box
          position="absolute"
          top={0}
          right={0}
          transform={"scale(2.5)"}
          w={"10%"}
          h={"100%"}
          bgImg="url(/images/padraoBG-01.svg)"
          bgRepeat="no-repeat"
          bgPos="center left"
          bgSize="cover"
          clipPath="inset(0 0 0 0)"
        />
        <VStack w="100%" gap={6} align="stretch" p={6}>
          <HStack gap={8}>
            <Image
              src="/images/logoBancoABC.svg"
              alt="Banco ABC"
              w="100px"
              h="auto"
            />
            <HStack gap={4} w={"50%"}>
              <Box
                bgColor={"gray.300"}
                color={"black"}
                borderRadius={"lg"}
                p={3}
              >
                <Text color={"black"} fontSize={"md"}>
                  Nome: Relatório Individual
                </Text>
              </Box>
              <Box
                bgColor={"gray.300"}
                color={"black"}
                borderRadius={"lg"}
                p={3}
              >
                <Text color={"black"} fontSize={"md"}>
                  Check-in: {reportData.averageCheckInCheckOut.checkIn}
                </Text>
              </Box>
              <Box
                bgColor={"gray.300"}
                color={"black"}
                borderRadius={"lg"}
                p={3}
              >
                <Text color={"black"} fontSize={"md"}>
                  Check-out: {reportData.averageCheckInCheckOut.checkOut}
                </Text>
              </Box>
            </HStack>
          </HStack>
          <HStack gap={20} px={20} align="stretch">
            <VStack gap={8} py={8} justifyContent="space-around">
              <Box bg={"#a6864a"} px={10} py={6} borderRadius={"4xl"}>
                <Text color={"white"} fontSize={"lg"} textAlign={"center"}>
                  Relatório gerado com base nas seleções realizadas. 
                  Os dados apresentados refletem as médias de proficiência 
                  para os cargos selecionados.
                </Text>
              </Box>
              <VStack gap={4} w={"50%"}>
                {reportData.averageDimensions.map((dimension, index) => (
                  <Box
                    key={index}
                    bgColor={"gray.300"}
                    color={"black"}
                    borderRadius={"lg"}
                    p={3}
                  >
                    <Text color={"black"} fontSize={"md"}>
                      {dimension.name} Check-in: {dimension.checkIn.average}
                    </Text>
                    <Text color={"black"} fontSize={"md"}>
                      {dimension.name} Check-out: {dimension.checkOut.average}
                    </Text>
                  </Box>
                ))}
              </VStack>
              <Text color={"white"} fontSize={"lg"} textAlign={"center"}>
                Dados coletados e processados automaticamente do sistema.
              </Text>
            </VStack>
            <Image
              src="/images/mandala.png"
              alt="Mandala"
              w="800px"
              zIndex={1}
            />
          </HStack>
        </VStack>
      </Box>
    );
  }

  // Multiple Results View
  return (
    <Box w="100%" position="relative" overflow="hidden" flexShrink={0}>
      <VStack w="100%" gap={8} align="stretch" p={6}>
        <HStack gap={8}>
          <Image
            src="/images/logoBancoABC.svg"
            alt="Banco ABC"
            w="100px"
            h="auto"
          />
          <HStack gap={4} w={"50%"}>
            <Box
              bgColor={"gray.300"}
              color={"black"}
              borderRadius={"lg"}
              py={3}
              px={6}
            >
              <Text color={"black"} fontSize={"md"}>
                Check-in
              </Text>
              <Text color={"black"} fontSize={"md"}>
                Média: {reportData.averageCheckInCheckOut.checkIn}
              </Text>
            </Box>
            <Box
              bgColor={"gray.300"}
              color={"black"}
              borderRadius={"lg"}
              py={3}
              px={6}
            >
              <Text color={"black"} fontSize={"md"}>
                Check-out
              </Text>
              <Text color={"black"} fontSize={"md"}>
                Média: {reportData.averageCheckInCheckOut.checkOut}
              </Text>
            </Box>
          </HStack>
        </HStack>
        <HStack gap={20} w={"80%"} alignSelf={"center"}>
          <Image src="/images/mandala.png" alt="Mandala" w="500px" />
          <Text fontSize={"3xl"}>
            "Relatório gerado com base nas seleções múltiplas realizadas. 
            Os dados apresentam uma visão consolidada das proficiências 
            dos cargos selecionados."
          </Text>
        </HStack>

        {/* Charts for each dimension */}
        {reportData.averageDimensions.map((dimension, index) => (
          <Box key={index} px={6}>
            <HStack
              w="100%"
              bg="#393B3D"
              borderRadius="4xl"
              gap={0}
              align="stretch"
            >
              <Flex
                flex={1.5}
                color="white"
                justify="center"
                align="center"
                direction="column"
                gap={4}
              >
                <HStack>
                  <Text fontSize="lg">{dimension.name}:</Text>
                  <Box
                    border="1px solid white"
                    borderRadius="md"
                    px={1}
                    py={1}
                  >
                    <Text fontSize="lg">{dimension.checkIn.average}</Text>
                  </Box>
                  <Box
                    border="1px solid white"
                    borderRadius="md"
                    px={1}
                    py={1}
                  >
                    <Text fontSize="lg">{dimension.checkOut.average}</Text>
                  </Box>
                </HStack>
              </Flex>

              <VStack
                flex={3}
                bg="#B2B2B2"
                borderLeftRadius="4xl"
                p={4}
                gap={4}
                h="200px"
                position={"relative"}
              >
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={transformToChartData(dimension)}
                    margin={{ top: 25, right: 60 }}
                  >
                    <XAxis dataKey="name" stroke="black" tickLine={false} />
                    <Tooltip cursor={{ fill: "rgba(255, 255, 255, 0.1)" }} />
                    <Bar dataKey="checkin" fill="#104862" barSize={24}>
                      <LabelList
                        dataKey="checkin"
                        position="top"
                        fill="black"
                        style={{ fontSize: "12px" }}
                      />
                    </Bar>
                    <Bar dataKey="checkout" fill="#00B0F0" barSize={24}>
                      <LabelList
                        dataKey="checkout"
                        position="top"
                        fill="black"
                        style={{ fontSize: "12px" }}
                      />
                    </Bar>
                  </BarChart>
                </ResponsiveContainer>
              </VStack>
            </HStack>
          </Box>
        ))}
      </VStack>
    </Box>
  );
}
