import {
  HStack,
  Image,
  Input,
  InputGroup,
  Text,
  Button,
} from "@chakra-ui/react";
import { LuSearch } from "react-icons/lu";

interface ReportsHeaderProps {
  searchTerm: string;
  onSearchChange: (term: string) => void;
  onGenerateReport: () => void;
  isGenerateDisabled: boolean;
  isGenerating: boolean;
}

export function ReportsHeader({
  searchTerm,
  onSearchChange,
  onGenerateReport,
  isGenerateDisabled,
  isGenerating,
}: ReportsHeaderProps) {
  return (
    <HStack w="100%" justifyContent="space-between" alignItems="center" mb={8}>
      <HStack gap={32}>
        <Image
          src="/images/logoBancoABC.svg"
          alt="Banco ABC"
          w="100px"
          h="auto"
        />
        <Text fontSize="40px">Filtro Visão Banco</Text>
      </HStack>
      <HStack gap={4}>
        <Button
          onClick={onGenerateReport}
          disabled={isGenerateDisabled}
          loading={isGenerating}
          bg="#a6864a"
          color="white"
          _hover={{
            bg: "#8b6f3d",
          }}
          _disabled={{
            bg: "gray.400",
            cursor: "not-allowed",
          }}
          size="lg"
          fontWeight="bold"
        >
          Gerar Relatório
        </Button>

        <InputGroup startElement={<LuSearch />} maxW="400px">
          <Input
            placeholder="Buscar por nome respondente"
            bg="white"
            border="1px solid"
            borderColor="gray.600"
            color="black"
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
            _placeholder={{ color: "gray.400" }}
            _focus={{
              borderColor: "blue.400",
              boxShadow: "0 0 0 1px #3182ce",
            }}
          />
        </InputGroup>
      </HStack>
    </HStack>
  );
}
